{"api": {"base_url": "https://yourdomain.com/api/v1", "timeout": 30000, "retry_attempts": 3, "headers": {"Content-Type": "application/json", "Accept": "application/json", "X-API-Version": "1.0"}}, "endpoints": {"auth": {"login": "/auth/login", "logout": "/auth/logout", "user": "/auth/user"}, "users": {"list": "/users", "show": "/users/{id}", "create": "/users", "update": "/users/{id}", "delete": "/users/{id}"}, "products": {"list": "/products", "show": "/products/{id}", "create": "/products", "update": "/products/{id}", "delete": "/products/{id}", "barcode": "/products/barcode/{barcode}", "categories": "/products/categories", "units": "/products/units"}, "api_keys": {"list": "/api-keys", "create": "/api-keys", "show": "/api-keys/{id}", "update": "/api-keys/{id}", "delete": "/api-keys/{id}", "regenerate": "/api-keys/{id}/regenerate", "permissions": "/api-keys/permissions"}, "app": {"me": "/app/me", "stats": "/app/stats"}}, "security": {"api_key_header": "X-API-Key", "token_header": "Authorization", "token_prefix": "Bearer "}, "cache": {"enabled": true, "duration": 300000, "endpoints": ["/products/categories", "/products/units", "/api-keys/permissions"]}, "pagination": {"default_per_page": 15, "max_per_page": 100}, "validation": {"required_fields": {"user": ["name", "email", "password", "type"], "product": ["name", "sku", "sale_price", "purchase_price", "quantity", "type"]}}}