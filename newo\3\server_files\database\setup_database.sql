-- إعد<PERSON> قاعدة البيانات لتطبيق Android
-- يجب تشغيل هذا الملف بصلاحيات المدير

-- إنشاء قاعدة البيانات إذا لم تكن موجودة
CREATE DATABASE IF NOT EXISTS ty CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE ty;

-- التأكد من وجود جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    email_verified_at TIMESTAMP NULL,
    password VARCHAR(255) NULL,
    plan INT NULL,
    plan_expire_date DATE NULL,
    type VARCHAR(100) NULL,
    storage_limit FLOAT DEFAULT 0.00,
    avatar VARCHAR(255) DEFAULT 'avatar.png',
    messenger_color VARCHAR(255) DEFAULT '#2180f3',
    lang VARCHAR(100) NULL,
    default_pipeline INT NULL,
    active_status BOOLEAN DEFAULT 0,
    delete_status INT DEFAULT 1,
    mode VARCHAR(10) DEFAULT 'light',
    dark_mode BOOLEAN DEFAULT 0,
    is_active INT DEFAULT 1,
    last_login_at DATETIME NULL,
    created_by INT DEFAULT 0,
    warehouse_id INT NULL,
    remember_token VARCHAR(100) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- التأكد من وجود جدول المنتجات والخدمات
CREATE TABLE IF NOT EXISTS product_services (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    sku VARCHAR(255) NOT NULL,
    sale_price DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    purchase_price DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    quantity INT NOT NULL DEFAULT 0,
    tax_id INT NULL,
    category_id INT NULL,
    unit_id INT NULL,
    type VARCHAR(50) NOT NULL DEFAULT 'product',
    sale_chartaccount_id INT NULL,
    expense_chartaccount_id INT NULL,
    created_by INT NOT NULL,
    expiry_date DATE NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_created_by (created_by),
    INDEX idx_sku (sku),
    INDEX idx_type (type)
);

-- التأكد من وجود جدول فئات المنتجات
CREATE TABLE IF NOT EXISTS product_service_categories (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    color VARCHAR(50) NULL,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- التأكد من وجود جدول وحدات المنتجات
CREATE TABLE IF NOT EXISTS product_service_units (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- التأكد من وجود جدول الضرائب
CREATE TABLE IF NOT EXISTS taxes (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    rate DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- إدراج بيانات افتراضية للفئات
INSERT IGNORE INTO product_service_categories (id, name, color, created_by) VALUES
(1, 'إلكترونيات', '#2196F3', 0),
(2, 'ملابس', '#4CAF50', 0),
(3, 'طعام ومشروبات', '#FF9800', 0),
(4, 'كتب ومكتبة', '#9C27B0', 0),
(5, 'أدوات منزلية', '#607D8B', 0);

-- إدراج بيانات افتراضية للوحدات
INSERT IGNORE INTO product_service_units (id, name, created_by) VALUES
(1, 'قطعة', 0),
(2, 'كيلوجرام', 0),
(3, 'لتر', 0),
(4, 'متر', 0),
(5, 'علبة', 0),
(6, 'حبة', 0);

-- إدراج بيانات افتراضية للضرائب
INSERT IGNORE INTO taxes (id, name, rate, created_by) VALUES
(1, 'ضريبة القيمة المضافة', 15.00, 0),
(2, 'معفى من الضريبة', 0.00, 0);

-- إنشاء مستخدم تجريبي
INSERT IGNORE INTO users (id, name, email, password, type, is_active, created_by) VALUES
(1, 'مدير النظام', '<EMAIL>', 'admin123', 'super admin', 1, 0),
(2, 'موظف تجريبي', '<EMAIL>', 'employee123', 'employee', 1, 1);

-- رسالة تأكيد
SELECT 'تم إعداد قاعدة البيانات بنجاح!' AS message;
