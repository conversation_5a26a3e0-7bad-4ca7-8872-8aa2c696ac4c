# دليل التثبيت والإعداد

## متطلبات النظام

### الخادم (Server)
- PHP 8.1 أو أحدث
- Laravel 10.x
- MySQL 5.7 أو أحدث
- Composer
- SSL Certificate (مطلوب للإنتاج)

### تطبيق Android
- Android 6.0 (API level 23) أو أحدث
- 50 MB مساحة تخزين فارغة
- اتصال بالإنترنت

## تثبيت النظام على الخادم

### 1. تثبيت قاعدة البيانات

```bash
# تشغيل migration لجدول مفاتيح API
php artisan migrate

# أو تشغيل migration محدد
php artisan migrate --path=/database/migrations/2024_01_01_000000_create_api_keys_table.php
```

### 2. إنشاء مفتاح API أولي

```bash
# تشغيل tinker
php artisan tinker

# إنشاء مفتاح API
$user = App\Models\User::first(); // أو المستخدم المطلوب
$apiKey = App\Models\ApiKey::create([
    'name' => 'Android App Key',
    'key' => App\Models\ApiKey::generateKey(),
    'user_id' => $user->id,
    'permissions' => ['*'], // جميع الصلاحيات
    'is_active' => true
]);

echo "API Key: " . $apiKey->key;
```

### 3. تكوين الخادم

#### إعدادات Apache (.htaccess)

```apache
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Handle API requests
    RewriteCond %{REQUEST_URI} ^/api/
    RewriteRule ^(.*)$ public/index.php [QSA,L]
    
    # Handle other requests
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^(.*)$ public/index.php [QSA,L]
</IfModule>

# Security headers for API
<IfModule mod_headers.c>
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-API-Key"
    Header always set Access-Control-Max-Age "3600"
</IfModule>
```

#### إعدادات Nginx

```nginx
server {
    listen 80;
    server_name yourdomain.com;
    root /path/to/your/project/public;
    index index.php;

    # API routes
    location /api/ {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # PHP processing
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # CORS headers
    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
    add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-API-Key";
}
```

### 4. اختبار API

```bash
# اختبار API info
curl -X GET "https://yourdomain.com/api/v1/info"

# اختبار مع API key
curl -X GET "https://yourdomain.com/api/v1/users" \
  -H "X-API-Key: your_api_key_here"
```

## تثبيت تطبيق Android

### 1. تحميل التطبيق

1. قم بتحميل ملف APK من مجلد `releases/`
2. أو احصل على رابط التحميل من المطور

### 2. تمكين التثبيت من مصادر غير معروفة

#### Android 8.0 وأحدث:
1. اذهب إلى **الإعدادات** > **الأمان والخصوصية**
2. اختر **تثبيت التطبيقات غير المعروفة**
3. اختر المتصفح أو مدير الملفات
4. فعل **السماح من هذا المصدر**

#### Android 7.1 وأقدم:
1. اذهب إلى **الإعدادات** > **الأمان**
2. فعل **مصادر غير معروفة**

### 3. تثبيت التطبيق

1. افتح ملف APK
2. اضغط على **تثبيت**
3. انتظر حتى اكتمال التثبيت
4. اضغط على **فتح**

## إعداد التطبيق

### 1. الإعداد الأولي

عند فتح التطبيق لأول مرة:

1. **عنوان الخادم**: أدخل عنوان API
   ```
   https://yourdomain.com/api/v1
   ```

2. **مفتاح API**: أدخل مفتاح API الذي حصلت عليه
   ```
   ak_your_api_key_here
   ```

3. **اختبار الاتصال**: اضغط على "اختبار الاتصال"

### 2. تسجيل الدخول

1. أدخل البريد الإلكتروني وكلمة المرور
2. اضغط على "تسجيل الدخول"
3. سيتم حفظ بيانات الدخول تلقائياً

### 3. التحقق من الصلاحيات

تأكد من أن مفتاح API يحتوي على الصلاحيات المطلوبة:

- `users:read` - لعرض المستخدمين
- `users:write` - لإنشاء وتحديث المستخدمين
- `products:read` - لعرض المنتجات
- `products:write` - لإنشاء وتحديث المنتجات

## استكشاف الأخطاء

### مشاكل الاتصال

#### خطأ "Unable to connect to server"
- تحقق من عنوان الخادم
- تأكد من وجود اتصال بالإنترنت
- تحقق من إعدادات الجدار الناري

#### خطأ "Invalid API Key"
- تحقق من صحة مفتاح API
- تأكد من أن المفتاح نشط وغير منتهي الصلاحية
- تحقق من الصلاحيات

#### خطأ "SSL Certificate Error"
- تأكد من صحة شهادة SSL
- أو استخدم HTTP للاختبار (غير آمن)

### مشاكل البيانات

#### "No data found"
- تحقق من وجود بيانات في قاعدة البيانات
- تأكد من صلاحيات القراءة

#### "Permission denied"
- تحقق من صلاحيات مفتاح API
- تأكد من أن المستخدم له الصلاحيات المطلوبة

### مشاكل الأداء

#### بطء في التحميل
- تحقق من سرعة الإنترنت
- قلل عدد النتائج في الصفحة الواحدة
- فعل التخزين المؤقت

## الصيانة

### تحديث مفاتيح API

```bash
# تجديد مفتاح API
php artisan tinker

$apiKey = App\Models\ApiKey::where('key', 'old_key')->first();
$apiKey->update(['key' => App\Models\ApiKey::generateKey()]);
echo "New API Key: " . $apiKey->key;
```

### مراقبة الاستخدام

```sql
-- عرض آخر استخدام لمفاتيح API
SELECT name, key, last_used_at, is_active 
FROM api_keys 
ORDER BY last_used_at DESC;

-- عرض المفاتيح المنتهية الصلاحية
SELECT name, key, expires_at 
FROM api_keys 
WHERE expires_at < NOW() AND is_active = 1;
```

### النسخ الاحتياطي

```bash
# نسخ احتياطي لقاعدة البيانات
mysqldump -u username -p database_name > backup.sql

# نسخ احتياطي للملفات
tar -czf backup.tar.gz /path/to/project
```

## الأمان

### أفضل الممارسات

1. **استخدم HTTPS دائماً في الإنتاج**
2. **قم بتجديد مفاتيح API بانتظام**
3. **حدد صلاحيات محددة لكل مفتاح**
4. **راقب استخدام API**
5. **احتفظ بنسخ احتياطية منتظمة**

### إعدادات الأمان

```php
// في ملف .env
API_RATE_LIMIT=60  // عدد الطلبات في الدقيقة
API_KEY_EXPIRY=365 // انتهاء صلاحية المفتاح بالأيام
```

## الدعم

للحصول على المساعدة:

1. راجع هذا الدليل أولاً
2. تحقق من ملف `api_documentation.md`
3. تواصل مع فريق التطوير

## سجل التحديثات

- **v1.0.0** - الإصدار الأولي مع إدارة المستخدمين
- **v1.1.0** - إضافة إدارة المنتجات والخدمات
- **v1.2.0** - تحسينات الأمان والأداء
