<?php

namespace App\Http\Middleware;

use App\Models\ApiKey;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class ApiKeyMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @param  string|null  $permission
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next, $permission = null)
    {
        $apiKey = $request->header('X-API-Key') ?? $request->get('api_key');

        if (!$apiKey) {
            return response()->json([
                'success' => false,
                'message' => 'API Key is required',
                'error' => 'MISSING_API_KEY'
            ], 401);
        }

        $keyModel = ApiKey::findValidKey($apiKey);

        if (!$keyModel) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid or expired API Key',
                'error' => 'INVALID_API_KEY'
            ], 401);
        }

        if (!$keyModel->isValid()) {
            return response()->json([
                'success' => false,
                'message' => 'API Key is not active or has expired',
                'error' => 'INACTIVE_API_KEY'
            ], 401);
        }

        // التحقق من الصلاحيات إذا تم تحديدها
        if ($permission && !$keyModel->hasPermission($permission)) {
            return response()->json([
                'success' => false,
                'message' => 'Insufficient permissions for this API endpoint',
                'error' => 'INSUFFICIENT_PERMISSIONS'
            ], 403);
        }

        // تحديث آخر استخدام للمفتاح
        $keyModel->updateLastUsed();

        // إضافة معلومات المفتاح والمستخدم إلى الطلب
        $request->merge([
            'api_key_model' => $keyModel,
            'api_user' => $keyModel->user
        ]);

        return $next($request);
    }
}
