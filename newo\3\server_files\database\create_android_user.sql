-- إنشاء مستخدم قاعدة البيانات المخصص لتطبيق Android
-- يجب تشغيل هذا الملف بصلاحيات المدير (root)

-- إنشاء مستخدم جديد للتطبيق
CREATE USER IF NOT EXISTS 'android_app'@'%' IDENTIFIED BY 'AndroidApp2024!';
CREATE USER IF NOT EXISTS 'android_app'@'localhost' IDENTIFIED BY 'AndroidApp2024!';

-- منح الصلاحيات المحددة للجداول المطلوبة

-- صلاحيات جدول المستخدمين (قراءة وتحديث آخر دخول فقط)
GRANT SELECT, UPDATE ON ty.users TO 'android_app'@'%';
GRANT SELECT, UPDATE ON ty.users TO 'android_app'@'localhost';

-- صلاحيات جدول المنتجات والخدمات (قراءة وكتابة وتحديث)
GRANT SELECT, INSERT, UPDATE ON ty.product_services TO 'android_app'@'%';
GRANT SELECT, INSERT, UPDATE ON ty.product_services TO 'android_app'@'localhost';

-- صلاحيات جدول فئات المنتجات (قراءة فقط)
GRANT SELECT ON ty.product_service_categories TO 'android_app'@'%';
GRANT SELECT ON ty.product_service_categories TO 'android_app'@'localhost';

-- صلاحيات جدول وحدات المنتجات (قراءة فقط)
GRANT SELECT ON ty.product_service_units TO 'android_app'@'%';
GRANT SELECT ON ty.product_service_units TO 'android_app'@'localhost';

-- صلاحيات جدول الضرائب (قراءة فقط)
GRANT SELECT ON ty.taxes TO 'android_app'@'%';
GRANT SELECT ON ty.taxes TO 'android_app'@'localhost';

-- تطبيق التغييرات
FLUSH PRIVILEGES;

-- التحقق من الصلاحيات الممنوحة
SHOW GRANTS FOR 'android_app'@'%';

-- اختبار الاتصال
SELECT 'تم إنشاء مستخدم التطبيق بنجاح!' AS message;

-- معلومات الاتصال للتطبيق:
-- Host: localhost أو عنوان IP الخادم
-- Database: ty
-- Username: android_app
-- Password: AndroidApp2024!
