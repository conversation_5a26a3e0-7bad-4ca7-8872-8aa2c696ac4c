<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ApiController;
use App\Http\Controllers\Api\PosApiController;
use App\Http\Controllers\Api\ProductApiController;
use App\Http\Controllers\Api\V1\UserApiController;
use App\Http\Controllers\Api\V1\ProductServiceApiController;
use App\Http\Controllers\Api\V1\ApiKeyController;
// use App\Http\Controllers\Api\CustomerApiController;
// use App\Http\Controllers\Api\WarehouseApiController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Authentication routes
Route::post('login', [ApiController::class, 'login']);

Route::group(['middleware' => ['auth:sanctum']], function () {
    // User management
    Route::post('logout', [ApiController::class, 'logout']);
    Route::get('user', [ApiController::class, 'getUser']);

    // Original routes
    Route::get('get-projects', [ApiController::class, 'getProjects']);
    Route::post('add-tracker', [ApiController::class, 'addTracker']);
    Route::post('stop-tracker', [ApiController::class, 'stopTracker']);
    Route::post('upload-photos', [ApiController::class, 'uploadImage']);

    // POS Desktop API Routes
    Route::prefix('pos')->group(function () {
        // POS Management
        Route::get('/', [PosApiController::class, 'index']);
        Route::post('/', [PosApiController::class, 'store']);
        Route::get('{id}', [PosApiController::class, 'show']);
        Route::put('{id}', [PosApiController::class, 'update']);
        Route::delete('{id}', [PosApiController::class, 'destroy']);

        // Cart Management
        Route::post('cart/add', [PosApiController::class, 'addToCart']);
        Route::put('cart/update', [PosApiController::class, 'updateCart']);
        Route::delete('cart/remove', [PosApiController::class, 'removeFromCart']);
        Route::delete('cart/empty', [PosApiController::class, 'emptyCart']);
        Route::get('cart', [PosApiController::class, 'getCart']);

        // Payment Processing
        Route::post('payment', [PosApiController::class, 'processPayment']);
        Route::get('payment/{id}', [PosApiController::class, 'getPayment']);

        // Invoice Management
        Route::get('invoices', [PosApiController::class, 'getInvoices']);
        Route::get('invoice/{id}/print', [PosApiController::class, 'printInvoice']);

        // Shift Management
        Route::post('shift/open', [PosApiController::class, 'openShift']);
        Route::post('shift/close', [PosApiController::class, 'closeShift']);
        Route::get('shift/current', [PosApiController::class, 'getCurrentShift']);
    });

    // Products API
    Route::prefix('products')->group(function () {
        Route::get('/', [ProductApiController::class, 'index']);
        Route::get('search', [ProductApiController::class, 'search']);
        Route::get('barcode/{barcode}', [ProductApiController::class, 'getByBarcode']);
        Route::get('categories', [ProductApiController::class, 'getCategories']);
        Route::get('warehouse/{warehouse_id}', [ProductApiController::class, 'getByWarehouse']);
        Route::get('{id}', [ProductApiController::class, 'show']);
    });

    // Customers API - Temporarily disabled
    /*
    Route::prefix('customers')->group(function () {
        Route::get('/', [CustomerApiController::class, 'index']);
        Route::post('/', [CustomerApiController::class, 'store']);
        Route::get('search', [CustomerApiController::class, 'search']);
        Route::get('{id}', [CustomerApiController::class, 'show']);
        Route::put('{id}', [CustomerApiController::class, 'update']);
    });
    */

    // Warehouses API - Temporarily disabled
    /*
    Route::prefix('warehouses')->group(function () {
        Route::get('/', [WarehouseApiController::class, 'index']);
        Route::get('{id}', [WarehouseApiController::class, 'show']);
        Route::get('{id}/products', [WarehouseApiController::class, 'getProducts']);
        Route::get('{id}/stock', [WarehouseApiController::class, 'getStock']);
    });
    */
});

// Include API V1 routes
require __DIR__ . '/api_v1.php';
