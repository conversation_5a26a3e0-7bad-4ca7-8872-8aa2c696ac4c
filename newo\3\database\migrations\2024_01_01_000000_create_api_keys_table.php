<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('api_keys', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // اسم المفتاح
            $table->string('key')->unique(); // المفتاح الفعلي
            $table->unsignedBigInteger('user_id'); // المستخدم المالك للمفتاح
            $table->json('permissions')->nullable(); // الصلاحيات
            $table->timestamp('last_used_at')->nullable(); // آخر استخدام
            $table->timestamp('expires_at')->nullable(); // تاريخ انتهاء الصلاحية
            $table->boolean('is_active')->default(true); // حالة المفتاح
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->index(['key', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('api_keys');
    }
};
