<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\V1\UserApiController;
use App\Http\Controllers\Api\V1\ProductServiceApiController;
use App\Http\Controllers\Api\V1\ApiKeyController;

/*
|--------------------------------------------------------------------------
| API V1 Routes
|--------------------------------------------------------------------------
|
| هنا يمكنك تسجيل API routes للتطبيق. هذه الـ routes
| يتم تحميلها بواسطة RouteServiceProvider ضمن مجموعة تحتوي
| على middleware "api". استمتع ببناء API الخاص بك!
|
*/

// Routes عامة بدون authentication
Route::prefix('v1')->group(function () {
    
    // تسجيل الدخول للحصول على API token
    Route::post('auth/login', [UserApiController::class, 'login']);
    
    // معلومات عامة عن API
    Route::get('info', function () {
        return response()->json([
            'success' => true,
            'message' => 'API is working',
            'version' => '1.0',
            'endpoints' => [
                'auth' => '/api/v1/auth/*',
                'users' => '/api/v1/users/*',
                'products' => '/api/v1/products/*',
                'api-keys' => '/api/v1/api-keys/*'
            ]
        ]);
    });

    // Routes محمية بـ API Key
    Route::middleware(['api.key'])->group(function () {
        
        // إدارة مفاتيح API
        Route::prefix('api-keys')->group(function () {
            Route::get('/', [ApiKeyController::class, 'index']);
            Route::post('/', [ApiKeyController::class, 'store']);
            Route::get('permissions', [ApiKeyController::class, 'getAvailablePermissions']);
            Route::get('{id}', [ApiKeyController::class, 'show']);
            Route::put('{id}', [ApiKeyController::class, 'update']);
            Route::delete('{id}', [ApiKeyController::class, 'destroy']);
            Route::post('{id}/regenerate', [ApiKeyController::class, 'regenerate']);
        });

        // إدارة المستخدمين
        Route::prefix('users')->group(function () {
            // قراءة المستخدمين
            Route::middleware(['api.key:users:read'])->group(function () {
                Route::get('/', [UserApiController::class, 'index']);
                Route::get('{id}', [UserApiController::class, 'show']);
            });

            // كتابة المستخدمين
            Route::middleware(['api.key:users:write'])->group(function () {
                Route::post('/', [UserApiController::class, 'store']);
                Route::put('{id}', [UserApiController::class, 'update']);
            });

            // حذف المستخدمين
            Route::middleware(['api.key:users:delete'])->group(function () {
                Route::delete('{id}', [UserApiController::class, 'destroy']);
            });
        });

        // إدارة المنتجات والخدمات
        Route::prefix('products')->group(function () {
            // قراءة المنتجات
            Route::middleware(['api.key:products:read'])->group(function () {
                Route::get('/', [ProductServiceApiController::class, 'index']);
                Route::get('categories', [ProductServiceApiController::class, 'getCategories']);
                Route::get('units', [ProductServiceApiController::class, 'getUnits']);
                Route::get('barcode/{barcode}', [ProductServiceApiController::class, 'searchByBarcode']);
                Route::get('{id}', [ProductServiceApiController::class, 'show']);
            });

            // كتابة المنتجات
            Route::middleware(['api.key:products:write'])->group(function () {
                Route::post('/', [ProductServiceApiController::class, 'store']);
                Route::put('{id}', [ProductServiceApiController::class, 'update']);
            });

            // حذف المنتجات
            Route::middleware(['api.key:products:delete'])->group(function () {
                Route::delete('{id}', [ProductServiceApiController::class, 'destroy']);
            });
        });

        // Routes إضافية للتطبيق
        Route::prefix('app')->group(function () {
            
            // معلومات المستخدم الحالي
            Route::get('me', function (Request $request) {
                return response()->json([
                    'success' => true,
                    'data' => $request->api_user->only(['id', 'name', 'email', 'type'])
                ]);
            });

            // إحصائيات سريعة
            Route::get('stats', function (Request $request) {
                $stats = [
                    'users_count' => \App\Models\User::count(),
                    'products_count' => \App\Models\ProductService::where('type', 'product')->count(),
                    'services_count' => \App\Models\ProductService::where('type', 'service')->count(),
                ];

                return response()->json([
                    'success' => true,
                    'data' => $stats
                ]);
            });
        });
    });
});

// Routes للـ Sanctum authentication (اختيارية)
Route::prefix('v1')->middleware(['auth:sanctum'])->group(function () {
    
    Route::post('auth/logout', function (Request $request) {
        $request->user()->currentAccessToken()->delete();
        
        return response()->json([
            'success' => true,
            'message' => 'Logged out successfully'
        ]);
    });

    Route::get('auth/user', function (Request $request) {
        return response()->json([
            'success' => true,
            'data' => $request->user()
        ]);
    });
});
