<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\ApiKey;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ApiKeyController extends Controller
{
    /**
     * عرض قائمة مفاتيح API للمستخدم
     */
    public function index(Request $request)
    {
        try {
            $user = $request->api_user;
            
            $apiKeys = ApiKey::where('user_id', $user->id)
                ->select(['id', 'name', 'key', 'permissions', 'last_used_at', 'expires_at', 'is_active', 'created_at'])
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json([
                'success' => true,
                'message' => 'API Keys retrieved successfully',
                'data' => $apiKeys
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving API keys',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * إنشاء مفتاح API جديد
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'permissions' => 'nullable|array',
                'permissions.*' => 'string',
                'expires_at' => 'nullable|date|after:now'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation error',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = $request->api_user;

            $apiKey = ApiKey::create([
                'name' => $request->name,
                'key' => ApiKey::generateKey(),
                'user_id' => $user->id,
                'permissions' => $request->permissions ?? ['users:read', 'products:read'],
                'expires_at' => $request->expires_at,
                'is_active' => true
            ]);

            return response()->json([
                'success' => true,
                'message' => 'API Key created successfully',
                'data' => $apiKey
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error creating API key',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * عرض مفتاح API محدد
     */
    public function show(Request $request, $id)
    {
        try {
            $user = $request->api_user;
            
            $apiKey = ApiKey::where('user_id', $user->id)
                ->where('id', $id)
                ->first();

            if (!$apiKey) {
                return response()->json([
                    'success' => false,
                    'message' => 'API Key not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'API Key retrieved successfully',
                'data' => $apiKey
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving API key',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * تحديث مفتاح API
     */
    public function update(Request $request, $id)
    {
        try {
            $user = $request->api_user;
            
            $apiKey = ApiKey::where('user_id', $user->id)
                ->where('id', $id)
                ->first();

            if (!$apiKey) {
                return response()->json([
                    'success' => false,
                    'message' => 'API Key not found'
                ], 404);
            }

            $validator = Validator::make($request->all(), [
                'name' => 'sometimes|required|string|max:255',
                'permissions' => 'sometimes|nullable|array',
                'permissions.*' => 'string',
                'expires_at' => 'sometimes|nullable|date|after:now',
                'is_active' => 'sometimes|boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation error',
                    'errors' => $validator->errors()
                ], 422);
            }

            $updateData = $request->only(['name', 'permissions', 'expires_at', 'is_active']);
            $apiKey->update($updateData);

            return response()->json([
                'success' => true,
                'message' => 'API Key updated successfully',
                'data' => $apiKey
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating API key',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * حذف مفتاح API
     */
    public function destroy(Request $request, $id)
    {
        try {
            $user = $request->api_user;
            
            $apiKey = ApiKey::where('user_id', $user->id)
                ->where('id', $id)
                ->first();

            if (!$apiKey) {
                return response()->json([
                    'success' => false,
                    'message' => 'API Key not found'
                ], 404);
            }

            $apiKey->delete();

            return response()->json([
                'success' => true,
                'message' => 'API Key deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting API key',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * تجديد مفتاح API (إنشاء مفتاح جديد)
     */
    public function regenerate(Request $request, $id)
    {
        try {
            $user = $request->api_user;
            
            $apiKey = ApiKey::where('user_id', $user->id)
                ->where('id', $id)
                ->first();

            if (!$apiKey) {
                return response()->json([
                    'success' => false,
                    'message' => 'API Key not found'
                ], 404);
            }

            $apiKey->update([
                'key' => ApiKey::generateKey()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'API Key regenerated successfully',
                'data' => $apiKey
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error regenerating API key',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * عرض الصلاحيات المتاحة
     */
    public function getAvailablePermissions()
    {
        $permissions = [
            'users:read' => 'قراءة المستخدمين',
            'users:write' => 'كتابة المستخدمين',
            'users:delete' => 'حذف المستخدمين',
            'products:read' => 'قراءة المنتجات',
            'products:write' => 'كتابة المنتجات',
            'products:delete' => 'حذف المنتجات',
            'orders:read' => 'قراءة الطلبات',
            'orders:write' => 'كتابة الطلبات',
            'reports:read' => 'قراءة التقارير',
            '*' => 'جميع الصلاحيات'
        ];

        return response()->json([
            'success' => true,
            'message' => 'Available permissions retrieved successfully',
            'data' => $permissions
        ]);
    }
}
