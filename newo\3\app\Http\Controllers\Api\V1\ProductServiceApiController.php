<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\ProductService;
use App\Models\ProductServiceCategory;
use App\Models\ProductServiceUnit;
use App\Models\Tax;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ProductServiceApiController extends Controller
{
    /**
     * عرض قائمة المنتجات والخدمات
     */
    public function index(Request $request)
    {
        try {
            $perPage = $request->get('per_page', 15);
            $search = $request->get('search');
            $type = $request->get('type'); // product or service

            $query = ProductService::with(['category', 'unit', 'taxes']);

            // البحث في الاسم و SKU
            if ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('sku', 'like', "%{$search}%");
                });
            }

            // فلترة حسب النوع
            if ($type) {
                $query->where('type', $type);
            }

            // فلترة حسب الفئة
            if ($request->has('category_id')) {
                $query->where('category_id', $request->get('category_id'));
            }

            $products = $query->select([
                'id', 'name', 'sku', 'sale_price', 'purchase_price',
                'quantity', 'type', 'category_id', 'unit_id', 'tax_id',
                'created_at', 'updated_at'
            ])->paginate($perPage);

            return response()->json([
                'success' => true,
                'message' => 'Products/Services retrieved successfully',
                'data' => $products
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving products/services',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * عرض منتج/خدمة محددة
     */
    public function show($id)
    {
        try {
            $product = ProductService::with(['category', 'unit', 'taxes'])
                ->find($id);

            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'Product/Service not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Product/Service retrieved successfully',
                'data' => $product
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving product/service',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * إنشاء منتج/خدمة جديدة
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'sku' => 'required|string|max:255|unique:product_services',
                'sale_price' => 'required|numeric|min:0',
                'purchase_price' => 'required|numeric|min:0',
                'quantity' => 'required|integer|min:0',
                'type' => 'required|in:product,service',
                'category_id' => 'nullable|exists:product_service_categories,id',
                'unit_id' => 'nullable|exists:product_service_units,id',
                'tax_id' => 'nullable|exists:taxes,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation error',
                    'errors' => $validator->errors()
                ], 422);
            }

            $product = ProductService::create([
                'name' => $request->name,
                'sku' => $request->sku,
                'sale_price' => $request->sale_price,
                'purchase_price' => $request->purchase_price,
                'quantity' => $request->quantity,
                'type' => $request->type,
                'category_id' => $request->category_id,
                'unit_id' => $request->unit_id,
                'tax_id' => $request->tax_id,
                'created_by' => $request->api_user->id ?? 1
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Product/Service created successfully',
                'data' => $product
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error creating product/service',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * تحديث منتج/خدمة
     */
    public function update(Request $request, $id)
    {
        try {
            $product = ProductService::find($id);

            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'Product/Service not found'
                ], 404);
            }

            $validator = Validator::make($request->all(), [
                'name' => 'sometimes|required|string|max:255',
                'sku' => 'sometimes|required|string|max:255|unique:product_services,sku,' . $id,
                'sale_price' => 'sometimes|required|numeric|min:0',
                'purchase_price' => 'sometimes|required|numeric|min:0',
                'quantity' => 'sometimes|required|integer|min:0',
                'type' => 'sometimes|required|in:product,service',
                'category_id' => 'nullable|exists:product_service_categories,id',
                'unit_id' => 'nullable|exists:product_service_units,id',
                'tax_id' => 'nullable|exists:taxes,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation error',
                    'errors' => $validator->errors()
                ], 422);
            }

            $updateData = $request->only([
                'name', 'sku', 'sale_price', 'purchase_price',
                'quantity', 'type', 'category_id', 'unit_id', 'tax_id'
            ]);

            $product->update($updateData);

            return response()->json([
                'success' => true,
                'message' => 'Product/Service updated successfully',
                'data' => $product
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating product/service',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * حذف منتج/خدمة
     */
    public function destroy($id)
    {
        try {
            $product = ProductService::find($id);

            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'Product/Service not found'
                ], 404);
            }

            $product->delete();

            return response()->json([
                'success' => true,
                'message' => 'Product/Service deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting product/service',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * البحث عن منتج بالباركود
     */
    public function searchByBarcode($barcode)
    {
        try {
            $product = ProductService::with(['category', 'unit', 'taxes'])
                ->where('sku', $barcode)
                ->first();

            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'Product not found with this barcode'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Product found successfully',
                'data' => $product
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error searching product',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * عرض الفئات
     */
    public function getCategories()
    {
        try {
            $categories = ProductServiceCategory::select(['id', 'name', 'color'])
                ->get();

            return response()->json([
                'success' => true,
                'message' => 'Categories retrieved successfully',
                'data' => $categories
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving categories',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * عرض الوحدات
     */
    public function getUnits()
    {
        try {
            $units = ProductServiceUnit::select(['id', 'name'])
                ->get();

            return response()->json([
                'success' => true,
                'message' => 'Units retrieved successfully',
                'data' => $units
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving units',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
