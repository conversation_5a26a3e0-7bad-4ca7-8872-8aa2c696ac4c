# وثائق API للتطبيق

## نظرة عامة

هذا المستند يوضح كيفية استخدام API الخاص بنظام إدارة نقاط البيع من تطبيق Android.

## المصادقة

### مفتاح API

جميع الطلبات تتطلب مفتاح API صالح يتم إرساله في header:

```
X-API-Key: ak_your_api_key_here
```

### الحصول على مفتاح API

1. سجل الدخول إلى لوحة التحكم
2. اذهب إلى إعدادات API
3. أنشئ مفتاح API جديد
4. حدد الصلاحيات المطلوبة

## الاستجابات

جميع الاستجابات تأتي بتنسيق JSON:

```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": { ... },
  "errors": { ... }
}
```

## رموز الحالة

- `200` - نجح الطلب
- `201` - تم إنشاء المورد بنجاح
- `400` - خطأ في البيانات المرسلة
- `401` - غير مصرح له (مفتاح API غير صالح)
- `403` - ممنوع (صلاحيات غير كافية)
- `404` - المورد غير موجود
- `422` - خطأ في التحقق من البيانات
- `500` - خطأ في الخادم

## المستخدمين

### قائمة المستخدمين

```
GET /api/v1/users
```

**Parameters:**
- `per_page` (optional): عدد النتائج في الصفحة (افتراضي: 15)
- `search` (optional): البحث في الاسم والبريد الإلكتروني
- `type` (optional): نوع المستخدم
- `is_active` (optional): الحالة النشطة

**Response:**
```json
{
  "success": true,
  "message": "Users retrieved successfully",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "name": "أحمد محمد",
        "email": "<EMAIL>",
        "type": "employee",
        "is_active": true,
        "created_at": "2024-01-01T00:00:00.000000Z",
        "updated_at": "2024-01-01T00:00:00.000000Z",
        "last_login_at": "2024-01-01T00:00:00.000000Z"
      }
    ],
    "per_page": 15,
    "total": 1
  }
}
```

### تفاصيل مستخدم

```
GET /api/v1/users/{id}
```

**Response:**
```json
{
  "success": true,
  "message": "User retrieved successfully",
  "data": {
    "id": 1,
    "name": "أحمد محمد",
    "email": "<EMAIL>",
    "type": "employee",
    "is_active": true,
    "warehouse_id": 1,
    "lang": "ar",
    "mode": "light",
    "created_at": "2024-01-01T00:00:00.000000Z",
    "updated_at": "2024-01-01T00:00:00.000000Z",
    "last_login_at": "2024-01-01T00:00:00.000000Z"
  }
}
```

### إنشاء مستخدم

```
POST /api/v1/users
```

**Body:**
```json
{
  "name": "محمد علي",
  "email": "<EMAIL>",
  "password": "password123",
  "type": "employee",
  "warehouse_id": 1
}
```

### تحديث مستخدم

```
PUT /api/v1/users/{id}
```

**Body:**
```json
{
  "name": "محمد علي المحدث",
  "is_active": false
}
```

### حذف مستخدم

```
DELETE /api/v1/users/{id}
```

## المنتجات والخدمات

### قائمة المنتجات

```
GET /api/v1/products
```

**Parameters:**
- `per_page` (optional): عدد النتائج في الصفحة
- `search` (optional): البحث في الاسم و SKU
- `type` (optional): نوع المنتج (product/service)
- `category_id` (optional): معرف الفئة

**Response:**
```json
{
  "success": true,
  "message": "Products/Services retrieved successfully",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "name": "لابتوب Dell",
        "sku": "DELL-001",
        "sale_price": 1500.00,
        "purchase_price": 1200.00,
        "quantity": 10,
        "type": "product",
        "category_id": 1,
        "unit_id": 1,
        "tax_id": 1,
        "created_at": "2024-01-01T00:00:00.000000Z",
        "updated_at": "2024-01-01T00:00:00.000000Z",
        "category": {
          "id": 1,
          "name": "إلكترونيات",
          "color": "#2196F3"
        },
        "unit": {
          "id": 1,
          "name": "قطعة"
        },
        "taxes": {
          "id": 1,
          "name": "ضريبة القيمة المضافة",
          "rate": 15.00
        }
      }
    ],
    "per_page": 15,
    "total": 1
  }
}
```

### البحث بالباركود

```
GET /api/v1/products/barcode/{barcode}
```

**Response:**
```json
{
  "success": true,
  "message": "Product found successfully",
  "data": {
    "id": 1,
    "name": "لابتوب Dell",
    "sku": "DELL-001",
    "sale_price": 1500.00,
    "purchase_price": 1200.00,
    "quantity": 10,
    "type": "product"
  }
}
```

### الفئات

```
GET /api/v1/products/categories
```

**Response:**
```json
{
  "success": true,
  "message": "Categories retrieved successfully",
  "data": [
    {
      "id": 1,
      "name": "إلكترونيات",
      "color": "#2196F3"
    },
    {
      "id": 2,
      "name": "ملابس",
      "color": "#4CAF50"
    }
  ]
}
```

### الوحدات

```
GET /api/v1/products/units
```

**Response:**
```json
{
  "success": true,
  "message": "Units retrieved successfully",
  "data": [
    {
      "id": 1,
      "name": "قطعة"
    },
    {
      "id": 2,
      "name": "كيلوجرام"
    }
  ]
}
```

## إدارة مفاتيح API

### قائمة مفاتيح API

```
GET /api/v1/api-keys
```

### إنشاء مفتاح API

```
POST /api/v1/api-keys
```

**Body:**
```json
{
  "name": "مفتاح تطبيق Android",
  "permissions": ["users:read", "products:read"],
  "expires_at": "2025-01-01T00:00:00Z"
}
```

### الصلاحيات المتاحة

```
GET /api/v1/api-keys/permissions
```

## معلومات التطبيق

### معلومات المستخدم الحالي

```
GET /api/v1/app/me
```

### إحصائيات سريعة

```
GET /api/v1/app/stats
```

**Response:**
```json
{
  "success": true,
  "data": {
    "users_count": 25,
    "products_count": 150,
    "services_count": 30
  }
}
```

## أمثلة على الاستخدام

### تسجيل الدخول

```javascript
const response = await fetch('/api/v1/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': 'your_api_key'
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password'
  })
});

const data = await response.json();
if (data.success) {
  // حفظ token للاستخدام في الطلبات القادمة
  localStorage.setItem('token', data.data.token);
}
```

### جلب قائمة المنتجات

```javascript
const response = await fetch('/api/v1/products?per_page=20&search=laptop', {
  headers: {
    'X-API-Key': 'your_api_key'
  }
});

const data = await response.json();
if (data.success) {
  console.log(data.data.data); // قائمة المنتجات
}
```
