# نظام API وتطبيق Android لإدارة نقاط البيع

## نظرة عامة

تم إنشاء نظام API متكامل مع تطبيق Android لإدارة نقاط البيع، يتضمن إدارة المستخدمين والمنتجات والخدمات.

## البنية المنشأة

### 1. نظام API

#### الملفات المنشأة:
```
├── database/migrations/
│   └── 2024_01_01_000000_create_api_keys_table.php
├── app/Models/
│   └── ApiKey.php
├── app/Http/Middleware/
│   └── ApiKeyMiddleware.php
├── app/Http/Controllers/Api/V1/
│   ├── UserApiController.php
│   ├── ProductServiceApiController.php
│   └── ApiKeyController.php
├── routes/
│   └── api_v1.php
└── bootstrap/app.php (محدث)
```

#### الميزات:
- ✅ نظام مفاتيح API مع صلاحيات محددة
- ✅ API للمستخدمين (CRUD كامل)
- ✅ API للمنتجات والخدمات (CRUD كامل)
- ✅ نظام المصادقة والتفويض
- ✅ البحث والفلترة
- ✅ Pagination
- ✅ معالجة الأخطاء

### 2. مجلد APK

#### الملفات المنشأة:
```
apk/
├── README.md
├── config/
│   ├── api_config.json
│   └── app_config.json
└── docs/
    ├── api_documentation.md
    └── installation.md
```

#### الميزات:
- ✅ إعدادات API مفصلة
- ✅ إعدادات التطبيق
- ✅ وثائق API شاملة
- ✅ دليل التثبيت والإعداد

## API Endpoints المتاحة

### المصادقة
- `POST /api/v1/auth/login` - تسجيل الدخول

### إدارة المستخدمين
- `GET /api/v1/users` - قائمة المستخدمين
- `GET /api/v1/users/{id}` - تفاصيل مستخدم
- `POST /api/v1/users` - إنشاء مستخدم
- `PUT /api/v1/users/{id}` - تحديث مستخدم
- `DELETE /api/v1/users/{id}` - حذف مستخدم

### إدارة المنتجات والخدمات
- `GET /api/v1/products` - قائمة المنتجات
- `GET /api/v1/products/{id}` - تفاصيل منتج
- `GET /api/v1/products/barcode/{barcode}` - البحث بالباركود
- `GET /api/v1/products/categories` - قائمة الفئات
- `GET /api/v1/products/units` - قائمة الوحدات
- `POST /api/v1/products` - إنشاء منتج
- `PUT /api/v1/products/{id}` - تحديث منتج
- `DELETE /api/v1/products/{id}` - حذف منتج

### إدارة مفاتيح API
- `GET /api/v1/api-keys` - قائمة مفاتيح API
- `POST /api/v1/api-keys` - إنشاء مفتاح API
- `GET /api/v1/api-keys/{id}` - تفاصيل مفتاح
- `PUT /api/v1/api-keys/{id}` - تحديث مفتاح
- `DELETE /api/v1/api-keys/{id}` - حذف مفتاح
- `POST /api/v1/api-keys/{id}/regenerate` - تجديد مفتاح

## خطوات التثبيت

### 1. تشغيل Migration

```bash
php artisan migrate
```

### 2. إنشاء مفتاح API أولي

```bash
php artisan tinker

$user = App\Models\User::first();
$apiKey = App\Models\ApiKey::create([
    'name' => 'Android App Key',
    'key' => App\Models\ApiKey::generateKey(),
    'user_id' => $user->id,
    'permissions' => ['*'],
    'is_active' => true
]);

echo "API Key: " . $apiKey->key;
exit
```

### 3. اختبار API

```bash
# اختبار معلومات API
curl -X GET "http://localhost/api/v1/info"

# اختبار مع مفتاح API
curl -X GET "http://localhost/api/v1/users" \
  -H "X-API-Key: your_api_key_here"
```

## الصلاحيات المتاحة

- `users:read` - قراءة المستخدمين
- `users:write` - كتابة المستخدمين
- `users:delete` - حذف المستخدمين
- `products:read` - قراءة المنتجات
- `products:write` - كتابة المنتجات
- `products:delete` - حذف المنتجات
- `*` - جميع الصلاحيات

## أمثلة على الاستخدام

### إنشاء مستخدم جديد

```bash
curl -X POST "http://localhost/api/v1/users" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your_api_key" \
  -d '{
    "name": "أحمد محمد",
    "email": "<EMAIL>",
    "password": "password123",
    "type": "employee"
  }'
```

### البحث في المنتجات

```bash
curl -X GET "http://localhost/api/v1/products?search=laptop&per_page=10" \
  -H "X-API-Key: your_api_key"
```

### البحث بالباركود

```bash
curl -X GET "http://localhost/api/v1/products/barcode/DELL-001" \
  -H "X-API-Key: your_api_key"
```

## الأمان

### Headers المطلوبة
- `X-API-Key`: مفتاح API صالح
- `Content-Type`: application/json (للطلبات POST/PUT)

### معالجة الأخطاء
جميع الاستجابات تأتي بتنسيق موحد:

```json
{
  "success": true/false,
  "message": "رسالة الحالة",
  "data": { ... },
  "errors": { ... }
}
```

## المرحلة التالية

### المرحلة الثالثة (مقترحة):
- [ ] إدارة العملاء
- [ ] إدارة المخازن
- [ ] عمليات نقاط البيع
- [ ] التقارير والإحصائيات

### المرحلة الرابعة (مقترحة):
- [ ] إشعارات push
- [ ] مزامنة offline
- [ ] طباعة الفواتير
- [ ] إدارة الخصومات

## الملفات المهمة

1. **وثائق API**: `apk/docs/api_documentation.md`
2. **دليل التثبيت**: `apk/docs/installation.md`
3. **إعدادات API**: `apk/config/api_config.json`
4. **إعدادات التطبيق**: `apk/config/app_config.json`

## الدعم والصيانة

- مراقبة استخدام مفاتيح API
- تجديد المفاتيح بانتظام
- النسخ الاحتياطية
- مراقبة الأداء

## ملاحظات مهمة

1. **الأمان**: استخدم HTTPS في الإنتاج
2. **الأداء**: فعل التخزين المؤقت للبيانات الثابتة
3. **المراقبة**: راقب استخدام API وسجل الأخطاء
4. **التحديثات**: احتفظ بإصدارات API متوافقة مع التطبيق

---

تم إنشاء النظام بنجاح! 🎉

المرحلة الأولى: ✅ ربط جدول المستخدمين بـ API
المرحلة الثانية: ✅ ربط جدول المنتجات والخدمات بـ API

النظام جاهز للاستخدام والتطوير!
