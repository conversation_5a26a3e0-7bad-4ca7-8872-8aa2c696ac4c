<?php
/**
 * اختبار الاتصال بقاعدة البيانات
 * استخدم هذا الملف للتأكد من صحة إعدادات قاعدة البيانات
 */

require_once 'config/database.php';

// إعداد الترميز
header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>";
echo "<html dir='rtl' lang='ar'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار الاتصال بقاعدة البيانات</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }";
echo ".container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".success { color: #4CAF50; background: #E8F5E8; padding: 15px; border-radius: 5px; margin: 10px 0; }";
echo ".error { color: #F44336; background: #FFEBEE; padding: 15px; border-radius: 5px; margin: 10px 0; }";
echo ".info { color: #2196F3; background: #E3F2FD; padding: 15px; border-radius: 5px; margin: 10px 0; }";
echo "table { width: 100%; border-collapse: collapse; margin: 20px 0; }";
echo "th, td { padding: 12px; text-align: right; border-bottom: 1px solid #ddd; }";
echo "th { background-color: #f2f2f2; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔍 اختبار الاتصال بقاعدة البيانات</h1>";

// اختبار الاتصال
echo "<h2>1. اختبار الاتصال الأساسي</h2>";
$pdo = getDatabaseConnection();

if ($pdo) {
    echo "<div class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح!</div>";
    
    // اختبار الجداول المطلوبة
    echo "<h2>2. فحص الجداول المطلوبة</h2>";
    $tables = ['users', 'product_services', 'product_service_categories', 'product_service_units', 'taxes'];
    
    echo "<table>";
    echo "<tr><th>اسم الجدول</th><th>الحالة</th><th>عدد السجلات</th></tr>";
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $result = $stmt->fetch();
            $count = $result['count'];
            echo "<tr>";
            echo "<td>$table</td>";
            echo "<td><span style='color: #4CAF50;'>✅ موجود</span></td>";
            echo "<td>$count</td>";
            echo "</tr>";
        } catch (PDOException $e) {
            echo "<tr>";
            echo "<td>$table</td>";
            echo "<td><span style='color: #F44336;'>❌ غير موجود</span></td>";
            echo "<td>-</td>";
            echo "</tr>";
        }
    }
    echo "</table>";
    
    // اختبار بيانات المستخدمين
    echo "<h2>3. اختبار بيانات المستخدمين</h2>";
    try {
        $stmt = $pdo->query("SELECT id, name, email, type, is_active FROM users LIMIT 5");
        $users = $stmt->fetchAll();
        
        if (count($users) > 0) {
            echo "<div class='success'>✅ تم العثور على " . count($users) . " مستخدم(ين)</div>";
            echo "<table>";
            echo "<tr><th>المعرف</th><th>الاسم</th><th>البريد الإلكتروني</th><th>النوع</th><th>نشط</th></tr>";
            foreach ($users as $user) {
                echo "<tr>";
                echo "<td>" . $user['id'] . "</td>";
                echo "<td>" . $user['name'] . "</td>";
                echo "<td>" . $user['email'] . "</td>";
                echo "<td>" . $user['type'] . "</td>";
                echo "<td>" . ($user['is_active'] ? 'نعم' : 'لا') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<div class='error'>❌ لا توجد بيانات مستخدمين</div>";
        }
    } catch (PDOException $e) {
        echo "<div class='error'>❌ خطأ في قراءة بيانات المستخدمين: " . $e->getMessage() . "</div>";
    }
    
    // اختبار بيانات الفئات والوحدات
    echo "<h2>4. اختبار البيانات المساعدة</h2>";
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM product_service_categories");
        $categories_count = $stmt->fetch()['count'];
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM product_service_units");
        $units_count = $stmt->fetch()['count'];
        
        echo "<div class='info'>";
        echo "📊 عدد الفئات: $categories_count<br>";
        echo "📊 عدد الوحدات: $units_count";
        echo "</div>";
        
    } catch (PDOException $e) {
        echo "<div class='error'>❌ خطأ في قراءة البيانات المساعدة: " . $e->getMessage() . "</div>";
    }
    
    // معلومات الاتصال
    echo "<h2>5. معلومات الاتصال</h2>";
    echo "<div class='info'>";
    echo "🖥️ الخادم: " . DB_HOST . "<br>";
    echo "🗄️ قاعدة البيانات: " . DB_NAME . "<br>";
    echo "👤 المستخدم: " . DB_USER . "<br>";
    echo "🔤 الترميز: " . DB_CHARSET;
    echo "</div>";
    
} else {
    echo "<div class='error'>❌ فشل الاتصال بقاعدة البيانات!</div>";
    echo "<div class='error'>تحقق من إعدادات قاعدة البيانات في ملف config/database.php</div>";
}

// معلومات الخادم
echo "<h2>6. معلومات الخادم</h2>";
echo "<div class='info'>";
echo "🐘 إصدار PHP: " . phpversion() . "<br>";
echo "🗄️ إصدار MySQL: ";
if ($pdo) {
    try {
        $stmt = $pdo->query("SELECT VERSION() as version");
        $version = $stmt->fetch()['version'];
        echo $version;
    } catch (PDOException $e) {
        echo "غير متاح";
    }
} else {
    echo "غير متاح";
}
echo "<br>";
echo "⏰ الوقت الحالي: " . date('Y-m-d H:i:s');
echo "</div>";

echo "<h2>7. خطوات التالية</h2>";
echo "<div class='info'>";
echo "1. إذا كانت جميع الاختبارات ناجحة، يمكنك الآن استخدام التطبيق<br>";
echo "2. تأكد من تحديث إعدادات الاتصال في تطبيق Android<br>";
echo "3. استخدم بيانات المستخدم التجريبي للاختبار<br>";
echo "4. يمكنك حذف هذا الملف بعد التأكد من عمل كل شيء";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";

// إغلاق الاتصال
if ($pdo) {
    closeDatabaseConnection($pdo);
}
?>
